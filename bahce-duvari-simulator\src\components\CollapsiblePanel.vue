<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <button
      @click="toggleOpen"
      class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200 rounded-t-lg"
    >
      <h3 class="font-medium text-gray-900">{{ title }}</h3>
      <svg
        :class="[
          'w-5 h-5 text-gray-500 transition-transform duration-200',
          isOpen ? 'transform rotate-180' : ''
        ]"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
      </svg>
    </button>
    
    <div
      :class="[
        'overflow-hidden transition-all duration-300 ease-in-out',
        isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
      ]"
    >
      <div class="p-4 border-t border-gray-100">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Props {
  title: string
  defaultOpen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultOpen: false
})

const isOpen = ref(false)

const toggleOpen = () => {
  isOpen.value = !isOpen.value
}

onMounted(() => {
  isOpen.value = props.defaultOpen
})
</script>
