import { defineStore } from 'pinia'
import { computed } from 'vue'
import { useCalculationStore, REBAR_WEIGHTS, type RebarDiameter } from './calculation'

export interface RebarCalculation {
  diameter: RebarDiameter
  totalLength: number // metre
  totalWeight: number // kg
  totalBars: number // adet (8m boy)
  description: string
}

export const useRebarStore = defineStore('rebar', () => {
  const calculationStore = useCalculationStore()

  // Temel boyuna demir hesabı
  const foundationLongitudinalRebar = computed((): RebarCalculation => {
    const { length } = calculationStore.dimensions
    const { diameter, count } = calculationStore.rebarDetails.foundationLongitudinal
    const totalLength = length * count
    const totalWeight = totalLength * REBAR_WEIGHTS[diameter]
    const totalBars = Math.ceil(totalLength / 8) // 8m boy demir
    
    return {
      diameter,
      totalLength,
      totalWeight,
      totalBars,
      description: `Temel Boyuna Demir Ø${diameter} - ${count} adet`
    }
  })

  // Temel etriye hesabı
  const foundationStirrupRebar = computed((): RebarCalculation => {
    const { length, foundationDepth, foundationWidth } = calculationStore.dimensions
    const { diameter, spacing } = calculationStore.rebarDetails.foundationStirrup
    const { concreteCover } = calculationStore.rebarDetails

    // Tek etriye çevresi hesabı - etriye çapı da dikkate alınmalı
    const stirrupDiameter = diameter / 10 // mm to cm
    const innerWidth = foundationWidth - (2 * (concreteCover + stirrupDiameter / 2))
    const innerDepth = foundationDepth - (2 * (concreteCover + stirrupDiameter / 2))
    const singleStirrupLength = (2 * (innerWidth + innerDepth)) / 100 + 0.2 // +20cm kanca payı

    // Toplam etriye adedi - doğru hesaplama
    const availableLength = length * 100 - 2 * concreteCover
    const stirrupCount = Math.floor(availableLength / spacing) + 1

    const totalLength = singleStirrupLength * stirrupCount
    const totalWeight = totalLength * REBAR_WEIGHTS[diameter]
    const totalBars = Math.ceil(totalLength / 8)

    return {
      diameter,
      totalLength,
      totalWeight,
      totalBars,
      description: `Temel Etriye Ø${diameter} - ${spacing}cm aralık (${stirrupCount} adet)`
    }
  })

  // Duvar dikey demir hesabı
  const wallVerticalRebar = computed((): RebarCalculation => {
    const { length, height, thickness } = calculationStore.dimensions
    const { diameter, spacing } = calculationStore.rebarDetails.wallVertical
    const { anchorageLength, concreteCover } = calculationStore.rebarDetails

    // Toplam dikey demir adedi - doğru hesaplama
    const availableLength = thickness - 2 * concreteCover
    const verticalRebarCount = Math.floor(availableLength / spacing) + 1

    // Tek dikey demirin uzunluğu (duvar yüksekliği + filiz payı)
    const singleRebarLength = height + (anchorageLength / 100)

    const totalLength = singleRebarLength * verticalRebarCount * length // duvar uzunluğu ile çarp
    const totalWeight = totalLength * REBAR_WEIGHTS[diameter]
    const totalBars = Math.ceil(totalLength / 8)

    return {
      diameter,
      totalLength,
      totalWeight,
      totalBars,
      description: `Duvar Dikey Demir Ø${diameter} - ${spacing}cm aralık (${verticalRebarCount} adet/m)`
    }
  })

  // Duvar yatay demir hesabı
  const wallHorizontalRebar = computed((): RebarCalculation => {
    const { length, height } = calculationStore.dimensions
    const { diameter, spacing } = calculationStore.rebarDetails.wallHorizontal
    const { concreteCover } = calculationStore.rebarDetails

    // Toplam yatay demir adedi - doğru hesaplama
    const availableHeight = height * 100 - 2 * concreteCover
    const horizontalRebarCount = Math.floor(availableHeight / spacing) + 1

    // Tek yatay demirin uzunluğu (duvar uzunluğu)
    const singleRebarLength = length

    const totalLength = singleRebarLength * horizontalRebarCount
    const totalWeight = totalLength * REBAR_WEIGHTS[diameter]
    const totalBars = Math.ceil(totalLength / 8)

    return {
      diameter,
      totalLength,
      totalWeight,
      totalBars,
      description: `Duvar Yatay Demir Ø${diameter} - ${spacing}cm aralık (${horizontalRebarCount} adet)`
    }
  })

  // Tüm demir hesaplamaları
  const allRebarCalculations = computed(() => [
    foundationLongitudinalRebar.value,
    foundationStirrupRebar.value,
    wallVerticalRebar.value,
    wallHorizontalRebar.value
  ])

  // Çapa göre gruplandırılmış demir hesabı
  const rebarByDiameter = computed(() => {
    const grouped = new Map<RebarDiameter, {
      totalLength: number
      totalWeight: number
      totalBars: number
      items: RebarCalculation[]
    }>()

    allRebarCalculations.value.forEach(calc => {
      const existing = grouped.get(calc.diameter)
      if (existing) {
        existing.totalLength += calc.totalLength
        existing.totalWeight += calc.totalWeight
        existing.totalBars += calc.totalBars
        existing.items.push(calc)
      } else {
        grouped.set(calc.diameter, {
          totalLength: calc.totalLength,
          totalWeight: calc.totalWeight,
          totalBars: calc.totalBars,
          items: [calc]
        })
      }
    })

    return Array.from(grouped.entries()).map(([diameter, data]) => ({
      diameter,
      ...data
    }))
  })

  // Toplam demir ağırlığı
  const totalRebarWeight = computed(() => {
    return allRebarCalculations.value.reduce((total, calc) => total + calc.totalWeight, 0)
  })

  // Fire payı ile birlikte toplam demir ağırlığı
  const totalRebarWeightWithWaste = computed(() => {
    const wasteMultiplier = 1 + (calculationStore.wastePercentage / 100)
    return totalRebarWeight.value * wasteMultiplier
  })

  // Toplam demir ağırlığını tona çevirme
  const totalRebarWeightInTons = computed(() => {
    return totalRebarWeightWithWaste.value / 1000
  })

  return {
    // Individual calculations
    foundationLongitudinalRebar,
    foundationStirrupRebar,
    wallVerticalRebar,
    wallHorizontalRebar,
    
    // Grouped calculations
    allRebarCalculations,
    rebarByDiameter,
    
    // Totals
    totalRebarWeight,
    totalRebarWeightWithWaste,
    totalRebarWeightInTons
  }
})
