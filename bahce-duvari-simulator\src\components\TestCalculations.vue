<template>
  <div class="card">
    <div class="card-header">
      <h3 class="section-title">🧪 Hesaplama <PERSON>leri</h3>
    </div>

    <div class="space-y-4">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 class="font-medium text-green-800 mb-2">Test Sonuçları</h4>
        <div class="space-y-2 text-sm">
          <div class="flex justify-between">
            <span class="text-green-700">Duvar <PERSON>cmi:</span>
            <span class="font-medium text-green-800">{{ wallVolume.toFixed(2) }} m³</span>
          </div>
          <div class="flex justify-between">
            <span class="text-green-700">Temel Hacmi:</span>
            <span class="font-medium text-green-800">{{ foundationVolume.toFixed(2) }} m³</span>
          </div>
          <div class="flex justify-between">
            <span class="text-green-700">Toplam Demir:</span>
            <span class="font-medium text-green-800">{{ totalRebarWeightInTons.toFixed(3) }} ton</span>
          </div>
          <div class="flex justify-between">
            <span class="text-green-700">Toplam Maliyet:</span>
            <span class="font-medium text-green-800">{{ formatCurrency(totalProjectCost) }}</span>
          </div>
        </div>
      </div>

      <button
        @click="runTests"
        class="btn-primary w-full"
      >
        Hesaplamaları Test Et
      </button>

      <div v-if="testResults.length > 0" class="space-y-2">
        <h4 class="font-medium text-gray-800">Test Sonuçları:</h4>
        <div v-for="(result, index) in testResults" :key="index" 
             :class="result.passed ? 'text-green-600' : 'text-red-600'"
             class="text-sm">
          {{ result.name }}: {{ result.passed ? '✅ Başarılı' : '❌ Başarısız' }}
          <span v-if="!result.passed" class="text-gray-500 ml-2">
            (Beklenen: {{ result.expected }}, Gerçek: {{ result.actual }})
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useCalculationStore } from '../stores/calculation'
import { useRebarStore } from '../stores/rebar'
import { usePricingStore } from '../stores/pricing'

const calculationStore = useCalculationStore()
const rebarStore = useRebarStore()
const pricingStore = usePricingStore()

const { wallVolume, foundationVolume } = storeToRefs(calculationStore)
const { totalRebarWeightInTons } = storeToRefs(rebarStore)
const { totalProjectCost } = storeToRefs(pricingStore)

const testResults = ref<Array<{
  name: string
  passed: boolean
  expected?: any
  actual?: any
}>>([])

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

const runTests = () => {
  testResults.value = []

  // Test 1: Duvar hacmi hesabı
  const expectedWallVolume = (10 * 2 * 20) / 100 // 4 m³
  testResults.value.push({
    name: 'Duvar Hacmi Hesabı',
    passed: Math.abs(wallVolume.value - expectedWallVolume) < 0.01,
    expected: expectedWallVolume,
    actual: wallVolume.value
  })

  // Test 2: Temel hacmi hesabı
  const expectedFoundationVolume = (10 * 80 * 40) / 10000 // 3.2 m³
  testResults.value.push({
    name: 'Temel Hacmi Hesabı',
    passed: Math.abs(foundationVolume.value - expectedFoundationVolume) < 0.01,
    expected: expectedFoundationVolume,
    actual: foundationVolume.value
  })

  // Test 3: Demir ağırlığı pozitif olmalı
  testResults.value.push({
    name: 'Demir Ağırlığı Pozitif',
    passed: totalRebarWeightInTons.value > 0,
    expected: '> 0',
    actual: totalRebarWeightInTons.value
  })

  // Test 4: Toplam maliyet pozitif olmalı
  testResults.value.push({
    name: 'Toplam Maliyet Pozitif',
    passed: totalProjectCost.value > 0,
    expected: '> 0',
    actual: totalProjectCost.value
  })

  // Test 5: Store'lar reaktif olmalı
  const initialVolume = wallVolume.value
  calculationStore.dimensions.length = 15
  const newVolume = wallVolume.value
  testResults.value.push({
    name: 'Reaktivite Testi',
    passed: newVolume !== initialVolume,
    expected: 'Değişken',
    actual: newVolume === initialVolume ? 'Sabit' : 'Değişken'
  })
  
  // Değeri geri al
  calculationStore.dimensions.length = 10
}
</script>
