<template>
  <div class="flex flex-col h-screen bg-gray-100">
    <!-- Üst Kontrol <PERSON>ğ<PERSON> -->
    <div class="bg-white border-b border-gray-200 px-6 py-3 shadow-sm">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <h1 class="text-xl font-bold text-gray-900 hidden sm:block">🏗️ Bahçe Duvarı Simülatörü</h1>
          <h1 class="text-lg font-bold text-gray-900 sm:hidden">🏗️ Duvar Simülatörü</h1>

          <!-- Mobile Menu Buttons -->
          <div class="flex items-center space-x-2 md:hidden">
            <button
              @click="showMobileMenu = !showMobileMenu"
              class="btn-secondary p-2"
              :class="{ 'bg-blue-100': showMobileMenu }"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
              </svg>
            </button>
            <button
              @click="showMobileResults = !showMobileResults"
              class="btn-secondary p-2 lg:hidden"
              :class="{ 'bg-blue-100': showMobileResults }"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </button>
          </div>

          <div class="hidden md:flex items-center space-x-3">
            <button
              @click="toggleFullscreen"
              class="btn-secondary flex items-center space-x-2"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
              </svg>
              <span class="hidden lg:inline">{{ isFullscreen ? 'Çıkış' : 'Tam Ekran' }}</span>
            </button>

            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700 hidden lg:inline">Görünüm:</label>
              <select v-model="viewMode" class="input-field w-24 lg:w-32">
                <option value="section">Kesit</option>
                <option value="3d">3D</option>
                <option value="plan">Plan</option>
                <option value="elevation">Cephe</option>
              </select>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">Zoom: {{ Math.round(zoomLevel * 100) }}%</span>
          <button @click="resetView" class="btn-secondary text-sm">Görünümü Sıfırla</button>
        </div>
      </div>
    </div>

    <!-- Mobile Overlay -->
    <div
      v-if="(showMobileMenu || showMobileResults) && !isFullscreen"
      @click="showMobileMenu = false; showMobileResults = false"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
    ></div>

    <!-- Ana İçerik Alanı -->
    <div class="flex-1 flex overflow-hidden relative">
      <!-- Sol Sidebar - Kontrol Panelleri -->
      <div
        :class="[
          'bg-white border-r border-gray-200 overflow-y-auto transition-all duration-300 shadow-lg',
          'w-80 lg:w-80 md:w-72',
          'md:relative md:translate-x-0',
          showMobileMenu ? 'fixed inset-y-0 left-0 z-50 translate-x-0' : 'fixed inset-y-0 left-0 z-50 -translate-x-full md:translate-x-0'
        ]"
        v-show="!isFullscreen"
      >
        <div class="p-4">
          <!-- Accordion Navigation -->
          <div class="space-y-2">
            <AccordionPanel
              title="📏 Boyutlar"
              :isOpen="activePanel === 'dimensions'"
              @toggle="togglePanel('dimensions')"
            >
              <DimensionsPanel />
            </AccordionPanel>

            <AccordionPanel
              title="🔩 Donatı"
              :isOpen="activePanel === 'rebar'"
              @toggle="togglePanel('rebar')"
            >
              <RebarPanel />
            </AccordionPanel>

            <AccordionPanel
              title="🎨 Yüzey İşlemleri"
              :isOpen="activePanel === 'surface'"
              @toggle="togglePanel('surface')"
            >
              <SurfaceTreatmentsPanel />
            </AccordionPanel>

            <AccordionPanel
              title="💰 Fiyatlar"
              :isOpen="activePanel === 'pricing'"
              @toggle="togglePanel('pricing')"
            >
              <PricingPanel />
            </AccordionPanel>
          </div>
        </div>
      </div>

      <!-- Orta Panel - Gelişmiş Görselleştirici -->
      <div class="flex-1 flex flex-col bg-white">
        <AdvancedWallVisualizer
          :viewMode="viewMode"
          :isFullscreen="isFullscreen"
          @zoom-change="handleZoomChange"
        />
      </div>

      <!-- Sağ Sidebar - Sonuçlar -->
      <div
        :class="[
          'bg-white border-l border-gray-200 overflow-y-auto transition-all duration-300 shadow-lg',
          'w-80 lg:w-80 md:w-72',
          'lg:relative lg:translate-x-0',
          showMobileResults ? 'fixed inset-y-0 right-0 z-50 translate-x-0' : 'fixed inset-y-0 right-0 z-50 translate-x-full lg:translate-x-0'
        ]"
        v-show="!isFullscreen"
      >
        <div class="p-4">
          <!-- Results Accordion -->
          <div class="space-y-2">
            <AccordionPanel
              title="⚡ Hızlı Özet"
              :isOpen="activeResultPanel === 'summary'"
              @toggle="toggleResultPanel('summary')"
            >
              <QuickSummary />
            </AccordionPanel>

            <AccordionPanel
              title="🧪 Test Sonuçları"
              :isOpen="activeResultPanel === 'tests'"
              @toggle="toggleResultPanel('tests')"
            >
              <TestCalculations />
            </AccordionPanel>

            <AccordionPanel
              title="📋 Detaylı Rapor"
              :isOpen="activeResultPanel === 'report'"
              @toggle="toggleResultPanel('report')"
            >
              <DetailedReport />
            </AccordionPanel>

            <AccordionPanel
              title="🔍 Donatı Doğrulama"
              :isOpen="activeResultPanel === 'validation'"
              @toggle="toggleResultPanel('validation')"
            >
              <RebarValidationTest />
            </AccordionPanel>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DimensionsPanel from './panels/DimensionsPanel.vue'
import RebarPanel from './panels/RebarPanel.vue'
import SurfaceTreatmentsPanel from './panels/SurfaceTreatmentsPanel.vue'
import PricingPanel from './panels/PricingPanel.vue'
import AdvancedWallVisualizer from './AdvancedWallVisualizer.vue'
import QuickSummary from './QuickSummary.vue'
import TestCalculations from './TestCalculations.vue'
import DetailedReport from './DetailedReport.vue'
import AccordionPanel from './AccordionPanel.vue'
import RebarValidationTest from './RebarValidationTest.vue'

// Layout state
const isFullscreen = ref(false)
const viewMode = ref<'section' | '3d' | 'plan' | 'elevation'>('section')
const zoomLevel = ref(1)

// Mobile state
const showMobileMenu = ref(false)
const showMobileResults = ref(false)

// Accordion state
const activePanel = ref<string>('dimensions')
const activeResultPanel = ref<string>('summary')

// Layout controls
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

const resetView = () => {
  zoomLevel.value = 1
}

const handleZoomChange = (newZoom: number) => {
  zoomLevel.value = newZoom
}

// Accordion controls
const togglePanel = (panelName: string) => {
  activePanel.value = activePanel.value === panelName ? '' : panelName
  // Close mobile menu when panel is selected
  if (showMobileMenu.value) {
    setTimeout(() => {
      showMobileMenu.value = false
    }, 300)
  }
}

const toggleResultPanel = (panelName: string) => {
  activeResultPanel.value = activeResultPanel.value === panelName ? '' : panelName
  // Close mobile results when panel is selected
  if (showMobileResults.value) {
    setTimeout(() => {
      showMobileResults.value = false
    }, 300)
  }
}
</script>
