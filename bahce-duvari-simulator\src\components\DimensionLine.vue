<template>
  <g class="dimension-line">
    <!-- Main dimension line -->
    <line
      :x1="x1"
      :y1="y1"
      :x2="x2"
      :y2="y2"
      stroke="#6b7280"
      stroke-width="1"
      stroke-dasharray="2,2"
    />
    
    <!-- Start arrow -->
    <line
      :x1="x1"
      :y1="vertical ? y1 - 5 : y1"
      :x2="x1"
      :y2="vertical ? y1 + 5 : y1"
      stroke="#6b7280"
      stroke-width="1"
    />
    <line
      v-if="!vertical"
      :x1="x1"
      :y1="y1 - 5"
      :x2="x1"
      :y2="y1 + 5"
      stroke="#6b7280"
      stroke-width="1"
    />
    
    <!-- End arrow -->
    <line
      :x1="x2"
      :y1="vertical ? y2 - 5 : y2"
      :x2="x2"
      :y2="vertical ? y2 + 5 : y2"
      stroke="#6b7280"
      stroke-width="1"
    />
    <line
      v-if="!vertical"
      :x1="x2"
      :y1="y2 - 5"
      :x2="x2"
      :y2="y2 + 5"
      stroke="#6b7280"
      stroke-width="1"
    />
    
    <!-- Label background -->
    <rect
      :x="labelX - labelWidth / 2"
      :y="labelY - 8"
      :width="labelWidth"
      height="16"
      fill="white"
      stroke="#d1d5db"
      stroke-width="1"
      rx="2"
    />
    
    <!-- Label text -->
    <text
      :x="labelX"
      :y="labelY + 4"
      text-anchor="middle"
      class="font-medium"
      :font-size="fontSize"
      fill="#374151"
    >
      {{ label }}
    </text>
  </g>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  x1: number
  y1: number
  x2: number
  y2: number
  label: string
  vertical?: boolean
  zoom?: number
}

const props = withDefaults(defineProps<Props>(), {
  vertical: false,
  zoom: 1
})

const labelX = computed(() => (props.x1 + props.x2) / 2)
const labelY = computed(() => (props.y1 + props.y2) / 2)
const labelWidth = computed(() => Math.max(60, props.label.length * 6 * props.zoom))
const fontSize = computed(() => Math.max(10, 12 * props.zoom))
</script>
