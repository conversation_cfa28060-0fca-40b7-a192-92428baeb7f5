@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-100 text-gray-900;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .card-header {
    @apply border-b border-gray-200 pb-4 mb-4;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }

  .input-field {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }

  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .section-title {
    @apply text-lg font-semibold text-gray-900 mb-4;
  }

  /* Accordion Panel Styles */
  .accordion-panel {
    @apply transition-all duration-300 ease-in-out;
  }

  .accordion-panel.active {
    @apply ring-2 ring-blue-500 ring-opacity-50;
  }

  .accordion-content {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Enhanced button styles */
  .btn-primary {
    @apply shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply shadow-sm hover:shadow-md;
  }

  .metric-card {
    @apply bg-gradient-to-r from-primary-50 to-primary-100 border border-primary-200 rounded-lg p-4;
  }

  .metric-value {
    @apply text-2xl font-bold text-primary-700;
  }

  .metric-label {
    @apply text-sm text-primary-600 font-medium;
  }
}

/* Custom utilities */
.animate-number {
  transition: all 0.3s ease-in-out;
}

/* Layout utilities */
.h-screen {
  height: 100vh;
}

.flex-1 {
  flex: 1 1 0%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.transition-all {
  transition: all 0.3s ease-in-out;
}

.duration-300 {
  transition-duration: 300ms;
}

.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Responsive width utilities */
.w-80 {
  width: 20rem;
}

.w-96 {
  width: 24rem;
}

.w-32 {
  width: 8rem;
}

.w-12 {
  width: 3rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

/* Spacing utilities */
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Padding utilities */
.p-1 {
  padding: 0.25rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

/* Color utilities */
.bg-blue-100 {
  background-color: #dbeafe;
}

.bg-blue-200 {
  background-color: #bfdbfe;
}

.text-blue-700 {
  color: #1d4ed8;
}

.text-blue-600 {
  color: #2563eb;
}

.hover\:bg-blue-200:hover {
  background-color: #bfdbfe;
}

.hover\:bg-gray-100:hover {
  background-color: #f3f4f6;
}

.hover\:bg-gray-200:hover {
  background-color: #e5e7eb;
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

/* Focus utilities */
.focus\:ring-blue-500:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Border utilities */
.rounded {
  border-radius: 0.25rem;
}

.border-gray-300 {
  border-color: #d1d5db;
}

/* Transform utilities */
.transform {
  transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1));
}

.rotate-180 {
  --tw-rotate: 180deg;
}

/* Opacity utilities */
.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

/* Max height utilities */
.max-h-0 {
  max-height: 0;
}

.max-h-screen {
  max-height: 100vh;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .w-80 {
    width: 16rem;
  }

  .w-96 {
    width: 20rem;
  }
}

@media (max-width: 640px) {
  .w-80 {
    width: 100%;
  }

  .w-96 {
    width: 100%;
  }

  .space-x-4 > * + * {
    margin-left: 0.5rem;
  }

  .space-x-2 > * + * {
    margin-left: 0.25rem;
  }
}
