<template>
  <div class="card">
    <div class="card-header">
      <h3 class="section-title">📐 <PERSON><PERSON></h3>
    </div>

    <div class="bg-gray-50 rounded-lg p-4">
      <svg
        :viewBox="`0 0 ${svgWidth} ${svgHeight}`"
        class="w-full h-auto border border-gray-300 bg-white rounded"
        style="min-height: 400px; max-height: 600px;"
      >
        <!-- Grid Background -->
        <defs>
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" stroke-width="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />

        <!-- Foundation -->
        <rect
          :x="foundationX"
          :y="foundationY"
          :width="foundationWidth"
          :height="foundationHeight"
          fill="#d1d5db"
          stroke="#6b7280"
          stroke-width="2"
        />

        <!-- Foundation Rebar -->
        <g v-if="showRebar">
          <!-- Longitudinal rebar -->
          <g v-for="(rebar, index) in foundationLongitudinalRebars" :key="`long-${index}`">
            <circle
              :cx="rebar.x"
              :cy="rebar.y"
              :r="getRebarRadius(rebarDetails.foundationLongitudinal.diameter)"
              fill="#8b5cf6"
              stroke="#7c3aed"
              stroke-width="1"
            />
          </g>

          <!-- Stirrups -->
          <g v-for="(stirrup, index) in foundationStirrups" :key="`stirrup-${index}`">
            <rect
              :x="stirrup.x"
              :y="stirrup.y"
              :width="stirrup.width"
              :height="stirrup.height"
              fill="none"
              stroke="#ef4444"
              :stroke-width="getRebarStrokeWidth(rebarDetails.foundationStirrup.diameter)"
            />
          </g>
        </g>

        <!-- Wall -->
        <rect
          :x="wallX"
          :y="wallY"
          :width="wallWidth"
          :height="wallHeight"
          fill="#e5e7eb"
          stroke="#6b7280"
          stroke-width="2"
        />

        <!-- Wall Rebar -->
        <g v-if="showRebar">
          <!-- Vertical rebar -->
          <g v-for="(rebar, index) in wallVerticalRebars" :key="`vert-${index}`">
            <line
              :x1="rebar.x"
              :y1="rebar.y1"
              :x2="rebar.x"
              :y2="rebar.y2"
              stroke="#10b981"
              :stroke-width="getRebarStrokeWidth(rebarDetails.wallVertical.diameter)"
            />
          </g>

          <!-- Horizontal rebar -->
          <g v-for="(rebar, index) in wallHorizontalRebars" :key="`horiz-${index}`">
            <line
              :x1="rebar.x1"
              :y1="rebar.y"
              :x2="rebar.x2"
              :y2="rebar.y"
              stroke="#f59e0b"
              :stroke-width="getRebarStrokeWidth(rebarDetails.wallHorizontal.diameter)"
            />
          </g>
        </g>

        <!-- Coping -->
        <rect
          v-if="surfaceTreatments.coping"
          :x="copingX"
          :y="copingY"
          :width="copingWidth"
          :height="copingHeight"
          fill="#fbbf24"
          stroke="#f59e0b"
          stroke-width="2"
        />

        <!-- Plaster layers -->
        <g v-if="surfaceTreatments.plaster.enabled">
          <!-- Left plaster -->
          <rect
            :x="wallX - plasterThickness"
            :y="wallY"
            :width="plasterThickness"
            :height="wallHeight"
            fill="#fef3c7"
            stroke="#f59e0b"
            stroke-width="1"
          />
          <!-- Right plaster (if double sided) -->
          <rect
            v-if="surfaceTreatments.plaster.sides === 'double'"
            :x="wallX + wallWidth"
            :y="wallY"
            :width="plasterThickness"
            :height="wallHeight"
            fill="#fef3c7"
            stroke="#f59e0b"
            stroke-width="1"
          />
        </g>

        <!-- Paint layers -->
        <g v-if="surfaceTreatments.paint.enabled">
          <!-- Left paint -->
          <rect
            :x="wallX - plasterThickness - paintThickness"
            :y="wallY"
            :width="paintThickness"
            :height="wallHeight"
            fill="#ddd6fe"
            stroke="#8b5cf6"
            stroke-width="1"
          />
          <!-- Right paint (if double sided) -->
          <rect
            v-if="surfaceTreatments.paint.sides === 'double'"
            :x="wallX + wallWidth + plasterThickness"
            :y="wallY"
            :width="paintThickness"
            :height="wallHeight"
            fill="#ddd6fe"
            stroke="#8b5cf6"
            stroke-width="1"
          />
        </g>

        <!-- Dimension lines and labels -->
        <g class="dimensions">
          <!-- Foundation width dimension -->
          <DimensionLine
            :x1="foundationX"
            :y1="foundationY + foundationHeight + 30"
            :x2="foundationX + foundationWidth"
            :y2="foundationY + foundationHeight + 30"
            :label="`${dimensions.foundationWidth} cm`"
          />

          <!-- Foundation depth dimension -->
          <DimensionLine
            :x1="foundationX - 30"
            :y1="foundationY"
            :x2="foundationX - 30"
            :y2="foundationY + foundationHeight"
            :label="`${dimensions.foundationDepth} cm`"
            vertical
          />

          <!-- Wall thickness dimension -->
          <DimensionLine
            :x1="wallX + wallWidth + 30"
            :y1="wallY"
            :x2="wallX + wallWidth + 30"
            :y2="wallY + wallHeight"
            :label="`${dimensions.thickness} cm`"
            vertical
          />

          <!-- Wall height dimension -->
          <DimensionLine
            :x1="wallX + wallWidth + 60"
            :y1="wallY"
            :x2="wallX + wallWidth + 60"
            :y2="wallY + wallHeight"
            :label="`${dimensions.height} m`"
            vertical
          />
        </g>

        <!-- Legend -->
        <g class="legend" transform="translate(20, 20)">
          <rect x="0" y="0" width="200" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="4"/>
          <text x="10" y="20" class="text-sm font-medium" fill="#374151">Malzeme Gösterimi</text>
          
          <g transform="translate(10, 35)">
            <rect x="0" y="0" width="15" height="10" fill="#e5e7eb" stroke="#6b7280"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Beton</text>
          </g>
          
          <g v-if="showRebar" transform="translate(10, 50)">
            <line x1="0" y1="5" x2="15" y2="5" stroke="#10b981" stroke-width="2"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Dikey Donatı</text>
          </g>
          
          <g v-if="showRebar" transform="translate(10, 65)">
            <line x1="0" y1="5" x2="15" y2="5" stroke="#f59e0b" stroke-width="2"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Yatay Donatı</text>
          </g>
          
          <g v-if="surfaceTreatments.plaster.enabled" transform="translate(10, 80)">
            <rect x="0" y="0" width="15" height="10" fill="#fef3c7" stroke="#f59e0b"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Sıva</text>
          </g>
          
          <g v-if="surfaceTreatments.paint.enabled" transform="translate(10, 95)">
            <rect x="0" y="0" width="15" height="10" fill="#ddd6fe" stroke="#8b5cf6"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Boya</text>
          </g>
        </g>

        <!-- Toggle rebar button -->
        <g class="cursor-pointer" @click="showRebar = !showRebar">
          <rect x="250" y="20" width="120" height="30" fill="white" stroke="#d1d5db" stroke-width="1" rx="4"/>
          <text x="310" y="38" class="text-sm font-medium" fill="#374151" text-anchor="middle">
            {{ showRebar ? '🔩 Donatı Gizle' : '🔩 Donatı Göster' }}
          </text>
        </g>
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useCalculationStore } from '../stores/calculation'
import DimensionLine from './DimensionLine.vue'

const calculationStore = useCalculationStore()
const { dimensions, rebarDetails, surfaceTreatments } = storeToRefs(calculationStore)

const showRebar = ref(true)

// SVG dimensions and scaling
const svgWidth = 800
const svgHeight = 600
const scale = 2 // pixels per cm

// Calculate positions and sizes
const foundationWidth = computed(() => dimensions.value.foundationWidth * scale)
const foundationHeight = computed(() => dimensions.value.foundationDepth * scale)
const foundationX = computed(() => (svgWidth - foundationWidth.value) / 2)
const foundationY = computed(() => svgHeight - foundationHeight.value - 100)

const wallWidth = computed(() => dimensions.value.thickness * scale)
const wallHeight = computed(() => dimensions.value.height * 100 * scale) // m to cm
const wallX = computed(() => foundationX.value + (foundationWidth.value - wallWidth.value) / 2)
const wallY = computed(() => foundationY.value - wallHeight.value)

const copingWidth = computed(() => (dimensions.value.thickness + 10) * scale)
const copingHeight = computed(() => 10 * scale)
const copingX = computed(() => wallX.value - 5 * scale)
const copingY = computed(() => wallY.value - copingHeight.value)

const plasterThickness = 2 * scale
const paintThickness = 1 * scale

// Rebar calculations
const foundationLongitudinalRebars = computed(() => {
  const rebars = []
  const count = rebarDetails.value.foundationLongitudinal.count
  const cover = rebarDetails.value.concreteCover * scale
  const rebarDiameter = rebarDetails.value.foundationLongitudinal.diameter * scale

  // Donatılar için kullanılabilir genişlik
  const availableWidth = foundationWidth.value - 2 * (cover + rebarDiameter / 2)
  const spacing = count > 1 ? availableWidth / (count - 1) : 0

  for (let i = 0; i < count; i++) {
    rebars.push({
      x: foundationX.value + cover + rebarDiameter / 2 + i * spacing,
      y: foundationY.value + foundationHeight.value - cover - rebarDiameter / 2
    })
  }
  return rebars
})

const foundationStirrups = computed(() => {
  const stirrups = []
  const spacing = rebarDetails.value.foundationStirrup.spacing * scale
  const cover = rebarDetails.value.concreteCover * scale
  const stirrupDiameter = rebarDetails.value.foundationStirrup.diameter * scale

  // Etriye sayısını doğru hesapla
  const availableLength = foundationWidth.value - 2 * cover
  const count = Math.floor(availableLength / spacing) + 1

  // Etriye boyutları - cover + yarım etriye çapı kadar içeride
  const stirrupWidth = foundationWidth.value - 2 * (cover + stirrupDiameter / 2)
  const stirrupHeight = foundationHeight.value - 2 * (cover + stirrupDiameter / 2)

  for (let i = 0; i < count; i++) {
    stirrups.push({
      x: foundationX.value + cover + stirrupDiameter / 2 + i * spacing,
      y: foundationY.value + cover + stirrupDiameter / 2,
      width: stirrupWidth,
      height: stirrupHeight
    })
  }
  return stirrups
})

const wallVerticalRebars = computed(() => {
  const rebars = []
  const spacing = rebarDetails.value.wallVertical.spacing * scale
  const cover = rebarDetails.value.concreteCover * scale
  const rebarDiameter = rebarDetails.value.wallVertical.diameter * scale
  const anchorage = rebarDetails.value.anchorageLength * scale

  // Donatı sayısını doğru hesapla
  const availableWidth = wallWidth.value - 2 * cover
  const count = Math.floor(availableWidth / spacing) + 1

  for (let i = 0; i < count; i++) {
    rebars.push({
      x: wallX.value + cover + rebarDiameter / 2 + i * spacing,
      y1: foundationY.value + foundationHeight.value - anchorage,
      y2: wallY.value + cover + rebarDiameter / 2
    })
  }
  return rebars
})

const wallHorizontalRebars = computed(() => {
  const rebars = []
  const spacing = rebarDetails.value.wallHorizontal.spacing * scale
  const cover = rebarDetails.value.concreteCover * scale
  const rebarDiameter = rebarDetails.value.wallHorizontal.diameter * scale

  // Donatı sayısını doğru hesapla
  const availableHeight = wallHeight.value - 2 * cover
  const count = Math.floor(availableHeight / spacing) + 1

  for (let i = 0; i < count; i++) {
    rebars.push({
      x1: wallX.value + cover + rebarDiameter / 2,
      x2: wallX.value + wallWidth.value - cover - rebarDiameter / 2,
      y: wallY.value + cover + rebarDiameter / 2 + i * spacing
    })
  }
  return rebars
})

const getRebarRadius = (diameter: number) => {
  return Math.max(2, diameter * scale / 10)
}

const getRebarStrokeWidth = (diameter: number) => {
  return Math.max(1, diameter * scale / 20)
}
</script>
