<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <button
      @click="$emit('toggle')"
      class="w-full px-4 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
      :class="{ 'bg-blue-50 border-b border-blue-100': isOpen }"
    >
      <h3 class="font-semibold text-gray-900 text-base">{{ title }}</h3>
      <div class="flex items-center space-x-2">
        <div 
          v-if="isOpen" 
          class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"
        ></div>
        <svg
          :class="[
            'w-5 h-5 text-gray-500 transition-transform duration-300 ease-in-out',
            isOpen ? 'transform rotate-180 text-blue-600' : ''
          ]"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
        </svg>
      </div>
    </button>
    
    <div
      :class="[
        'transition-all duration-300 ease-in-out overflow-hidden',
        isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
      ]"
    >
      <div class="p-4 bg-gray-50 border-t border-gray-100">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  isOpen: boolean
}

defineProps<Props>()
defineEmits<{
  toggle: []
}>()
</script>

<style scoped>
/* Smooth height transition */
.transition-all {
  transition-property: max-height, opacity, transform;
}

/* Enhanced focus styles */
button:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Active state styling */
.bg-blue-50 {
  background-color: rgb(239, 246, 255);
}
</style>
